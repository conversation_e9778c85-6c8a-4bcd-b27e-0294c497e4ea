{% extends "base.html" %}

{% block title %}{% if is_edit %}Edit{% else %}Create{% endif %} Exam | PYQs Admin | GPT Sir{% endblock %}

{% block head %}
<style>
  .create-exam-container {
    max-width: 900px;
    margin: 0 auto;
    padding: 20px;
  }

  .create-exam-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
  }

  .back-btn {
    background-color: var(--beige);
    color: var(--gunmetal);
    padding: 8px 16px;
    border-radius: 6px;
    text-decoration: none;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: background-color 0.3s;
  }

  .back-btn:hover {
    background-color: #e5e0c5;
  }

  .create-exam-form {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 30px;
  }

  .form-group {
    margin-bottom: 25px;
  }

  .form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: var(--gunmetal);
  }

  .form-control {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 1rem;
    transition: border-color 0.3s;
  }

  .form-control:focus {
    border-color: var(--tea-green);
    outline: none;
  }

  .form-row {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }

  .form-actions {
    display: flex;
    justify-content: flex-end;
    margin-top: 30px;
  }

  .submit-btn {
    background-color: var(--tea-green);
    color: var(--gunmetal);
    border: none;
    padding: 12px 25px;
    border-radius: 6px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.3s;
  }

  .submit-btn:hover {
    background-color: #b5e0c0;
  }

  .error-message {
    background-color: #ffebee;
    color: #c62828;
    padding: 15px;
    border-radius: 6px;
    margin-bottom: 20px;
    border-left: 4px solid #c62828;
    font-weight: 500;
    display: flex;
    align-items: center;
  }

  .error-message::before {
    content: '⚠️';
    margin-right: 10px;
    font-size: 1.2rem;
  }

  .field-hint {
    font-size: 0.85rem;
    color: #666;
    margin-top: 5px;
  }

  /* Textarea styles */
  .syllabus-textarea {
    width: 100%;
    min-height: 200px;
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 1rem;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
    line-height: 1.5;
    resize: vertical;
    transition: border-color 0.3s, box-shadow 0.3s;
    background-color: #fafafa;
  }

  .syllabus-textarea:focus {
    border-color: var(--tea-green);
    outline: none;
    box-shadow: 0 0 0 3px rgba(181, 224, 192, 0.2);
    background-color: white;
  }

  .syllabus-textarea::placeholder {
    color: #999;
    font-style: italic;
  }

  /* Loading spinner */
  .loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    border-top-color: var(--tea-green);
    animation: spin 1s ease-in-out infinite;
    margin-left: 10px;
  }

  @keyframes spin {
    to { transform: rotate(360deg); }
  }

  .hidden {
    display: none;
  }
</style>

{% endblock %}

{% block content %}
<div class="create-exam-container">
  <div class="create-exam-header">
    <h1>{% if is_edit %}Edit{% else %}Create{% endif %} Exam</h1>
    <a href="/pyqs_admin/exams" class="back-btn">
      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
        <path fill-rule="evenodd" d="M15 8a.5.5 0 0 0-.5-.5H2.707l3.147-3.146a.5.5 0 1 0-.708-.708l-4 4a.5.5 0 0 0 0 .708l4 4a.5.5 0 0 0 .708-.708L2.707 8.5H14.5A.5.5 0 0 0 15 8z"/>
      </svg>
      Back to Exams
    </a>
  </div>

  {% if error %}
    <div class="error-message">
      {{ error }}
    </div>
  {% endif %}

  <form method="post" class="create-exam-form">
    <div class="form-row">
      <div class="form-group">
        <label for="level" id="level-label">Level *</label>
        <select id="level" name="level" class="form-control" required>
          <option value="">Select a level</option>
          {% for level in levels %}
            <option value="{{ level.name }}" {% if is_edit and exam.level == level.name %}selected{% endif %}>{{ level.name }}</option>
          {% endfor %}
        </select>
      </div>

      <div class="form-group">
        <label for="syllabus" id="syllabus-label">Syllabus *</label>
        <select id="syllabus" name="syllabus" class="form-control" required {% if not is_edit %}disabled{% endif %}>
          <option value="">Select a syllabus</option>
          {% if is_edit and syllabi %}
            {% for syllabus_item in syllabi %}
              <option value="{{ syllabus_item.syllabus }}" {% if exam.syllabus == syllabus_item.syllabus %}selected{% endif %}>{{ syllabus_item.syllabus }}</option>
            {% endfor %}
          {% endif %}
        </select>
        <div id="syllabus-loading" class="loading hidden"></div>
      </div>
    </div>

    <div class="form-row">
      <div class="form-group">
        <label for="grade" id="grade-label">Grade *</label>
        <select id="grade" name="grade" class="form-control" required {% if not is_edit %}disabled{% endif %}>
          <option value="">Select a grade</option>
          {% if is_edit and grades %}
            {% for grade_item in grades %}
              <option value="{{ grade_item.grade }}" {% if exam.grade == grade_item.grade %}selected{% endif %}>{{ grade_item.grade }}</option>
            {% endfor %}
          {% endif %}
        </select>
        <div id="grade-loading" class="loading hidden"></div>
      </div>

      <div class="form-group">
        <label for="subject" id="subject-label">Subject *</label>
        <select id="subject" name="subject" class="form-control" required {% if not is_edit %}disabled{% endif %}>
          <option value="">Select a subject</option>
          {% if is_edit and subjects %}
            {% for subject_item in subjects %}
              <option value="{{ subject_item.subject }}" {% if exam.subject == subject_item.subject %}selected{% endif %}>{{ subject_item.subject }}</option>
            {% endfor %}
          {% endif %}
        </select>
        <div id="subject-loading" class="loading hidden"></div>
      </div>
    </div>

    <div class="form-group">
      <label for="exam_name">Exam Name *</label>
      <input type="text" id="exam_name" name="exam_name" class="form-control" required value="{{ exam.exam_name if is_edit else '' }}">
    </div>

    <div class="form-group">
      <label for="syllabus_text">Syllabus Text</label>
      <textarea id="syllabus_text" name="syllabus_text" class="syllabus-textarea" placeholder="Enter syllabus text here...">{{ exam.syllabus_text if is_edit else '' }}</textarea>
      <div class="field-hint">You can enter detailed syllabus information here. This field supports plain text formatting.</div>
    </div>

    <div class="form-actions">
      <button type="submit" class="submit-btn">{% if is_edit %}Update{% else %}Create{% endif %} Exam</button>
    </div>
  </form>
</div>
{% endblock %}

{% block scripts %}
<script>
  // Cascading dropdowns
  document.addEventListener('DOMContentLoaded', function() {
    const levelSelect = document.getElementById('level');
    const syllabusSelect = document.getElementById('syllabus');
    const gradeSelect = document.getElementById('grade');
    const subjectSelect = document.getElementById('subject');

    const syllabusLoading = document.getElementById('syllabus-loading');
    const gradeLoading = document.getElementById('grade-loading');
    const subjectLoading = document.getElementById('subject-loading');

    // Function to fetch data from API
    async function fetchData(url) {
      try {
        const response = await fetch(url);
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        return await response.json();
      } catch (error) {
        console.error('Error fetching data:', error);
        return null;
      }
    }

    // Function to populate a select element
    function populateSelect(select, data, valueKey, textKey) {
      // Clear existing options except the first one
      while (select.options.length > 1) {
        select.remove(1);
      }

      // Add new options
      data.forEach(item => {
        const option = document.createElement('option');
        option.value = item[valueKey];
        option.textContent = item[textKey];
        select.appendChild(option);
      });

      // Enable the select
      select.disabled = false;
    }

    // Function to update dropdown placeholders based on selected values
    function updatePlaceholders() {
      // Keep all labels consistent
      // No changes to labels or placeholders
    }

    // Level change event
    levelSelect.addEventListener('change', async function() {
      const level = this.value;

      // Reset and disable dependent dropdowns
      syllabusSelect.value = '';
      gradeSelect.value = '';
      subjectSelect.value = '';

      syllabusSelect.disabled = true;
      gradeSelect.disabled = true;
      subjectSelect.disabled = true;

      if (!level) return;

      // No need to update placeholders

      // Show loading indicator
      syllabusLoading.classList.remove('hidden');

      // Fetch syllabi for the selected level
      const data = await fetchData(`/pyqs_admin/api/syllabi/${level}`);

      // Hide loading indicator
      syllabusLoading.classList.add('hidden');

      if (data && data.syllabi) {
        populateSelect(syllabusSelect, data.syllabi, 'syllabus', 'syllabus');
      }
    });

    // Syllabus change event
    syllabusSelect.addEventListener('change', async function() {
      const syllabus = this.value;

      // Reset and disable dependent dropdowns
      gradeSelect.value = '';
      subjectSelect.value = '';

      gradeSelect.disabled = true;
      subjectSelect.disabled = true;

      if (!syllabus) return;

      // Update placeholders based on selected level and syllabus
      updatePlaceholders();

      // Show loading indicators
      gradeLoading.classList.remove('hidden');
      subjectLoading.classList.remove('hidden');

      // Fetch grades and subjects sequentially
      const gradeData = await fetchData(`/pyqs_admin/api/grades/${syllabus}`);
      const subjectData = await fetchData(`/pyqs_admin/api/subjects/${syllabus}`);


      // Hide loading indicators
      gradeLoading.classList.add('hidden');
      subjectLoading.classList.add('hidden');

      if (gradeData && gradeData.grades) {
        populateSelect(gradeSelect, gradeData.grades, 'grade', 'grade');
      }

      if (subjectData && subjectData.subjects) {
        populateSelect(subjectSelect, subjectData.subjects, 'subject', 'subject');
      }

      // Update placeholders again after populating the dropdowns
      updatePlaceholders();
    });

    // Initialize placeholders if we're in edit mode and values are pre-selected
    if (levelSelect.value) {
      updatePlaceholders();
    }
  });
</script>
{% endblock %}
