{% extends "base.html" %}

{% block title %}PYQs Admin | GPT Sir{% endblock %}

{% block head %}
<style>
  .pyqs-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
  }

  .pyqs-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
  }

  .create-btn {
    background-color: var(--tea-green);
    color: var(--gunmetal);
    padding: 10px 20px;
    border-radius: 6px;
    text-decoration: none;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: background-color 0.3s;
  }

  .create-btn:hover {
    background-color: #b5e0c0;
  }

  .search-container {
    display: flex;
    margin-bottom: 20px;
    max-width: 500px;
  }

  .search-input {
    flex: 1;
    padding: 10px 15px;
    border: 1px solid #ddd;
    border-radius: 6px 0 0 6px;
    font-size: 1rem;
  }

  .search-btn {
    background-color: var(--tea-green);
    color: var(--gunmetal);
    border: none;
    padding: 10px 20px;
    border-radius: 0 6px 6px 0;
    cursor: pointer;
    transition: background-color 0.3s;
  }

  .search-btn:hover {
    background-color: #b5e0c0;
  }

  .exams-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-bottom: 30px;
  }

  .exam-item {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: transform 0.3s, box-shadow 0.3s;
    display: flex;
    align-items: center;
  }

  .exam-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
  }

  .exam-content {
    padding: 20px;
    flex: 1;
    display: flex;
    align-items: center;
  }

  .exam-name-container {
    flex: 1;
  }

  .exam-name {
    margin: 0 0 5px 0;
    font-size: 1.2rem;
    color: var(--gunmetal);
  }

  .exam-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-top: 5px;
  }

  .exam-meta-item {
    display: flex;
    align-items: center;
    font-size: 0.9rem;
    background-color: #f5f5f5;
    padding: 4px 10px;
    border-radius: 20px;
  }

  .meta-label {
    font-weight: 600;
    color: #555;
    margin-right: 5px;
  }

  .meta-value {
    color: var(--gunmetal);
  }

  .exam-actions {
    display: flex;
    padding: 0 20px;
  }

  .action-btn {
    padding: 8px 16px;
    border-radius: 6px;
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s;
    display: flex;
    align-items: center;
    gap: 5px;
  }

  .view-btn {
    background-color: var(--light-blue);
    color: var(--white);
    border: 1px solid #0069d9;
  }

  .view-btn:hover {
    background-color: #0056b3;
    border-color: #0056b3;
  }

  .edit-btn {
    background-color: var(--beige);
    color: var(--gunmetal);
    border: 1px solid #e5e0c5;
  }

  .edit-btn:hover {
    background-color: #e5e0c5;
    transform: translateY(-2px);
  }

  .pagination {
    display: flex;
    justify-content: center;
    gap: 5px;
    margin-top: 30px;
  }

  .page-item {
    display: inline-block;
  }

  .page-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    text-decoration: none;
    color: var(--gunmetal);
    font-weight: 500;
    transition: background-color 0.3s;
  }

  .page-link:hover {
    background-color: #f0f0f0;
  }

  .page-item.active .page-link {
    background-color: var(--tea-green);
    color: var(--gunmetal);
  }

  .page-item.disabled .page-link {
    color: #aaa;
    pointer-events: none;
  }

  .no-exams {
    text-align: center;
    padding: 40px 0;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  }

  .no-exams p {
    margin-bottom: 20px;
    color: #555;
    font-size: 1.1rem;
  }

  .no-exams a {
    color: var(--gunmetal);
    text-decoration: underline;
  }
</style>
{% endblock %}

{% block content %}
<div class="pyqs-container">
  <div class="pyqs-header">
    <h1>PYQs Admin</h1>
    <a href="/pyqs_admin/exams/create" class="create-btn">
      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
        <path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"/>
      </svg>
      Create New Exam
    </a>
  </div>

  <form action="/pyqs_admin/exams" method="get" class="search-container">
    <input type="text" name="search" placeholder="Search exams..." class="search-input" value="{{ search }}">
    <button type="submit" class="search-btn">Search</button>
  </form>

  {% if error %}
    <div class="error-message">
      {{ error }}
    </div>
  {% endif %}

  {% if exams %}
    <div class="exams-list">
      {% for exam in exams %}
        <div class="exam-item">
          <div class="exam-content">
            <div class="exam-name-container">
              <h3 class="exam-name">{{ exam.exam_name }}</h3>
              <div class="exam-meta">
                <div class="exam-meta-item">
                  <span class="meta-label">Level:</span>
                  <span class="meta-value">{{ exam.level }}</span>
                </div>
                <div class="exam-meta-item">
                  <span class="meta-label">Syllabus:</span>
                  <span class="meta-value">{{ exam.syllabus }}</span>
                </div>
                <div class="exam-meta-item">
                  <span class="meta-label">Grade:</span>
                  <span class="meta-value">{{ exam.grade }}</span>
                </div>
                <div class="exam-meta-item">
                  <span class="meta-label">Subject:</span>
                  <span class="meta-value">{{ exam.subject }}</span>
                </div>
              </div>
            </div>
          </div>
          <div class="exam-actions">
            <a href="/pyqs_admin/exams/edit/{{ exam.id }}" class="action-btn edit-btn">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                <path d="M12.146.146a.5.5 0 0 1 .708 0l3 3a.5.5 0 0 1 0 .708l-10 10a.5.5 0 0 1-.168.11l-5 2a.5.5 0 0 1-.65-.65l2-5a.5.5 0 0 1 .11-.168l10-10zM11.207 2.5 13.5 4.793 14.793 3.5 12.5 1.207 11.207 2.5zm1.586 3L10.5 3.207 4 9.707V10h.5a.5.5 0 0 1 .5.5v.5h.5a.5.5 0 0 1 .5.5v.5h.293l6.5-6.5zm-9.761 5.175-.106.106-1.528 3.821 3.821-1.528.106-.106A.5.5 0 0 1 5 12.5V12h-.5a.5.5 0 0 1-.5-.5V11h-.5a.5.5 0 0 1-.468-.325z"/>
              </svg>
              Edit
            </a>
          </div>
        </div>
      {% endfor %}
    </div>

    {% if pages > 1 %}
      <nav class="pagination">
        <div class="page-item {% if page == 1 %}disabled{% endif %}">
          <a class="page-link" href="/pyqs_admin/exams?page={{ page - 1 }}&limit={{ limit }}{% if search %}&search={{ search }}{% endif %}" aria-label="Previous">
            &laquo;
          </a>
        </div>

        {% for p in range(1, pages + 1) %}
          <div class="page-item {% if p == page %}active{% endif %}">
            <a class="page-link" href="/pyqs_admin/exams?page={{ p }}&limit={{ limit }}{% if search %}&search={{ search }}{% endif %}">{{ p }}</a>
          </div>
        {% endfor %}

        <div class="page-item {% if page == pages %}disabled{% endif %}">
          <a class="page-link" href="/pyqs_admin/exams?page={{ page + 1 }}&limit={{ limit }}{% if search %}&search={{ search }}{% endif %}" aria-label="Next">
            &raquo;
          </a>
        </div>
      </nav>
    {% endif %}
  {% else %}
    <div class="no-exams">
      <p>No exams found. {% if search %}Try a different search term or {% endif %}<a href="/pyqs_admin/exams/create">create your first exam</a>.</p>
    </div>
  {% endif %}
</div>
{% endblock %}
